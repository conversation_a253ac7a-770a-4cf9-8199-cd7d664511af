import 'package:bloomg_flutter/features/video_gallery/models/gallery_filter.dart';
import 'package:bloomg_flutter/features/video_gallery/models/video_item.dart';
import 'package:bloomg_flutter/features/video_gallery/repository/video_gallery_repository.dart';
import 'package:bloomg_flutter/features/video_gallery/services/video_gallery_service.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';

class MockVideoGalleryService extends Mo<PERSON> implements VideoGalleryService {}

void main() {
  group('VideoGalleryRepository', () {
    late VideoGalleryRepository repository;
    late MockVideoGalleryService mockVideoGalleryService;
    late List<VideoItem> sampleVideos;

    setUp(() {
      mockVideoGalleryService = MockVideoGalleryService();
      repository = VideoGalleryRepository(mockVideoGalleryService);

      sampleVideos = [
        VideoItem(
          id: 'video-1',
          fileName: 'face_verification_2024-01-15_10-30-45.mp4',
          filePath: '/path/to/video1.mp4',
          fileSize: 1024000,
          createdAt: DateTime(2024, 1, 15, 10, 30, 45),
          duration: const Duration(seconds: 9),
          qualityScore: 85.5,
        ),
        VideoItem(
          id: 'video-2',
          fileName: 'face_verification_2024-01-14_09-15-30.mp4',
          filePath: '/path/to/video2.mp4',
          fileSize: 2048000,
          createdAt: DateTime(2024, 1, 14, 9, 15, 30),
          duration: const Duration(seconds: 9),
          qualityScore: 92,
        ),
        VideoItem(
          id: 'video-3',
          fileName: 'face_verification_2024-01-16_14-45-20.mp4',
          filePath: '/path/to/video3.mp4',
          fileSize: 1536000,
          createdAt: DateTime(2024, 1, 16, 14, 45, 20),
          duration: const Duration(seconds: 9),
          qualityScore: 78.3,
        ),
      ];
    });

    group('loadVideos', () {
      test('should load videos successfully', () async {
        when(() => mockVideoGalleryService.getVideos())
            .thenAnswer((_) async => sampleVideos);

        final result = await repository.loadVideos();

        expect(result, sampleVideos);
        verify(() => mockVideoGalleryService.getVideos()).called(1);
      });

      test('should return empty list when no videos exist', () async {
        when(() => mockVideoGalleryService.getVideos())
            .thenAnswer((_) async => <VideoItem>[]);

        final result = await repository.loadVideos();

        expect(result, isEmpty);
        verify(() => mockVideoGalleryService.getVideos()).called(1);
      });

      test('should handle service errors', () async {
        when(() => mockVideoGalleryService.getVideos())
            .thenThrow(Exception('Service error'));

        expect(
          () => repository.loadVideos(),
          throwsException,
        );
        verify(() => mockVideoGalleryService.getVideos()).called(1);
      });
    });

    group('filterVideos', () {
      test('should return all videos with default filter', () {
        const filter = GalleryFilter();

        final result = repository.filterVideos(sampleVideos, filter);

        expect(result.length, 3);
        // Should be sorted by date descending (default)
        expect(result[0].id, 'video-3'); // 2024-01-16
        expect(result[1].id, 'video-1'); // 2024-01-15
        expect(result[2].id, 'video-2'); // 2024-01-14
      });

      test('should sort by date ascending', () {
        const filter = GalleryFilter(sortBy: GallerySortBy.dateAscending);

        final result = repository.filterVideos(sampleVideos, filter);

        expect(result[0].id, 'video-2'); // 2024-01-14
        expect(result[1].id, 'video-1'); // 2024-01-15
        expect(result[2].id, 'video-3'); // 2024-01-16
      });

      test('should sort by quality descending', () {
        const filter = GalleryFilter(sortBy: GallerySortBy.qualityDescending);

        final result = repository.filterVideos(sampleVideos, filter);

        expect(result[0].qualityScore, 92.0); // video-2
        expect(result[1].qualityScore, 85.5); // video-1
        expect(result[2].qualityScore, 78.3); // video-3
      });

      test('should sort by quality ascending', () {
        const filter = GalleryFilter(sortBy: SortBy.qualityAscending);

        final result = repository.filterVideos(sampleVideos, filter);

        expect(result[0].qualityScore, 78.3); // video-3
        expect(result[1].qualityScore, 85.5); // video-1
        expect(result[2].qualityScore, 92.0); // video-2
      });

      test('should filter by high quality', () {
        const filter = GalleryFilter(qualityFilter: QualityFilter.highQuality);

        final result = repository.filterVideos(sampleVideos, filter);

        expect(result.length, 2);
        expect(result.every((video) => video.qualityScore >= 80), true);
      });

      test('should filter by medium quality', () {
        const filter =
            GalleryFilter(qualityFilter: QualityFilter.mediumQuality);

        final result = repository.filterVideos(sampleVideos, filter);

        expect(result.length, 1);
        expect(result[0].qualityScore, 78.3);
        expect(
          result.every(
            (video) => video.qualityScore >= 60 && video.qualityScore < 80,
          ),
          true,
        );
      });

      test('should filter by low quality', () {
        final lowQualityVideos = [
          ...sampleVideos,
          VideoItem(
            id: 'video-4',
            fileName: 'low_quality.mp4',
            filePath: '/path/to/video4.mp4',
            fileSize: 512000,
            createdAt: DateTime(2024, 1, 17),
            qualityScore: 45,
          ),
        ];

        const filter = GalleryFilter(qualityFilter: QualityFilter.lowQuality);

        final result = repository.filterVideos(lowQualityVideos, filter);

        expect(result.length, 1);
        expect(result[0].qualityScore, 45.0);
        expect(result.every((video) => video.qualityScore < 60), true);
      });

      test('should filter by date range', () {
        final dateRange = DateRange(
          start: DateTime(2024, 1, 14),
          end: DateTime(2024, 1, 15),
        );
        final filter = GalleryFilter(dateRange: dateRange);

        final result = repository.filterVideos(sampleVideos, filter);

        expect(result.length, 2);
        expect(result.any((video) => video.id == 'video-1'), true);
        expect(result.any((video) => video.id == 'video-2'), true);
        expect(result.any((video) => video.id == 'video-3'), false);
      });

      test('should filter by search query', () {
        const filter = GalleryFilter(searchQuery: '2024-01-15');

        final result = repository.filterVideos(sampleVideos, filter);

        expect(result.length, 1);
        expect(result[0].id, 'video-1');
      });

      test('should apply multiple filters', () {
        final dateRange = DateRange(
          start: DateTime(2024, 1, 14),
          end: DateTime(2024, 1, 16),
        );
        const filter = GalleryFilter(
          sortBy: SortBy.qualityDescending,
          qualityFilter: QualityFilter.highQuality,
          dateRange: dateRange,
        );

        final result = repository.filterVideos(sampleVideos, filter);

        expect(result.length, 2);
        expect(result[0].qualityScore, 92.0); // video-2 (highest quality)
        expect(result[1].qualityScore, 85.5); // video-1
        expect(result.every((video) => video.qualityScore >= 80), true);
      });

      test('should handle empty video list', () {
        const filter = GalleryFilter();

        final result = repository.filterVideos([], filter);

        expect(result, isEmpty);
      });

      test('should handle case-insensitive search', () {
        const filter = GalleryFilter(searchQuery: 'FACE_VERIFICATION');

        final result = repository.filterVideos(sampleVideos, filter);

        expect(result.length, 3); // All videos contain 'face_verification'
      });
    });

    group('deleteVideo', () {
      test('should delete video successfully', () async {
        const videoId = 'video-1';
        when(() => mockVideoGalleryService.deleteVideo(videoId))
            .thenAnswer((_) async => true);

        final result = await repository.deleteVideo(videoId);

        expect(result, true);
        verify(() => mockVideoGalleryService.deleteVideo(videoId)).called(1);
      });

      test('should return false when deletion fails', () async {
        const videoId = 'video-1';
        when(() => mockVideoGalleryService.deleteVideo(videoId))
            .thenAnswer((_) async => false);

        final result = await repository.deleteVideo(videoId);

        expect(result, false);
        verify(() => mockVideoGalleryService.deleteVideo(videoId)).called(1);
      });

      test('should handle service errors during deletion', () async {
        const videoId = 'video-1';
        when(() => mockVideoGalleryService.deleteVideo(videoId))
            .thenThrow(Exception('Delete failed'));

        expect(
          () => repository.deleteVideo(videoId),
          throwsException,
        );
        verify(() => mockVideoGalleryService.deleteVideo(videoId)).called(1);
      });
    });

    group('generateThumbnail', () {
      test('should generate thumbnail successfully', () async {
        final video = sampleVideos[0];
        final updatedVideo = video.copyWith(
          thumbnailPath: '/path/to/thumbnail.jpg',
        );

        when(() => mockVideoGalleryService.generateThumbnail(video))
            .thenAnswer((_) async => '/path/to/thumbnail.jpg');

        final result = await repository.generateThumbnail(video);

        expect(result, isNotNull);
        expect(result!.thumbnailPath, '/path/to/thumbnail.jpg');
        verify(() => mockVideoGalleryService.generateThumbnail(video))
            .called(1);
      });

      test('should return null when thumbnail generation fails', () async {
        final video = sampleVideos[0];

        when(() => mockVideoGalleryService.generateThumbnail(video))
            .thenAnswer((_) async => null);

        final result = await repository.generateThumbnail(video);

        expect(result, isNull);
        verify(() => mockVideoGalleryService.generateThumbnail(video))
            .called(1);
      });

      test('should handle service errors during thumbnail generation',
          () async {
        final video = sampleVideos[0];

        when(() => mockVideoGalleryService.generateThumbnail(video))
            .thenThrow(Exception('Thumbnail generation failed'));

        expect(
          () => repository.generateThumbnail(video),
          throwsException,
        );
        verify(() => mockVideoGalleryService.generateThumbnail(video))
            .called(1);
      });
    });

    group('getStorageStats', () {
      test('should get storage stats successfully', () async {
        final expectedStats = {
          'totalVideos': 3,
          'totalSize': 4608000,
          'averageQuality': 85.27,
          'oldestVideo': DateTime(2024, 1, 14),
          'newestVideo': DateTime(2024, 1, 16),
        };

        when(() => mockVideoGalleryService.getStorageStats())
            .thenAnswer((_) async => expectedStats);

        final result = await repository.getStorageStats();

        expect(result, expectedStats);
        verify(() => mockVideoGalleryService.getStorageStats()).called(1);
      });

      test('should handle empty storage stats', () async {
        final expectedStats = {
          'totalVideos': 0,
          'totalSize': 0,
          'averageQuality': 0.0,
          'oldestVideo': null,
          'newestVideo': null,
        };

        when(() => mockVideoGalleryService.getStorageStats())
            .thenAnswer((_) async => expectedStats);

        final result = await repository.getStorageStats();

        expect(result, expectedStats);
        verify(() => mockVideoGalleryService.getStorageStats()).called(1);
      });

      test('should handle service errors during stats retrieval', () async {
        when(() => mockVideoGalleryService.getStorageStats())
            .thenThrow(Exception('Stats retrieval failed'));

        expect(
          () => repository.getStorageStats(),
          throwsException,
        );
        verify(() => mockVideoGalleryService.getStorageStats()).called(1);
      });
    });

    group('refreshVideos', () {
      test('should refresh videos successfully', () async {
        when(() => mockVideoGalleryService.getVideos())
            .thenAnswer((_) async => sampleVideos);

        final result = await repository.refreshVideos();

        expect(result, sampleVideos);
        verify(() => mockVideoGalleryService.getVideos()).called(1);
      });
    });
  });
}
